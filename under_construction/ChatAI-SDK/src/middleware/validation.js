const { v4: uuidv4, validate: validateUUID } = require('uuid');

/**
 * Validate chat request
 */
const validateChatRequest = (req, res, next) => {
  const { appId } = req.params;
  const { query, sessionId, stream, includeHistory } = req.query; // Changed from req.body to req.query
  const authToken = req.headers.authorization;

  // Validate appId (should be UUID)
  if (!appId || !validateUUID(appId)) {
    return res.status(400).json({
      error: true,
      message: 'Valid appId (UUID) is required'
    });
  }

  // Validate query
  if (!query || typeof query !== 'string' || query.trim().length === 0) {
    return res.status(400).json({
      error: true,
      message: 'Query is required and must be a non-empty string'
    });
  }

  // Validate query length
  if (query.length > 2000) {
    return res.status(400).json({
      error: true,
      message: 'Query is too long (max 2000 characters)'
    });
  }

  // Validate authorization header
  if (!authToken || !authToken.startsWith('Bearer ')) {
    return res.status(401).json({
      error: true,
      message: 'Authorization header with Bearer token is required'
    });
  }

  // Note: apiKey validation is temporarily disabled for development
  // TODO: Enable when Kong API Gateway is ready

  // Validate sessionId if provided
  if (sessionId && !validateUUID(sessionId)) {
    return res.status(400).json({
      error: true,
      message: 'SessionId must be a valid UUID if provided'
    });
  }

  // Validate stream parameter (query params are strings, so check for 'true'/'false')
  if (stream !== undefined && !['true', 'false'].includes(stream)) {
    return res.status(400).json({
      error: true,
      message: 'Stream parameter must be "true" or "false"'
    });
  }

  // Validate includeHistory parameter (query params are strings, so check for 'true'/'false')
  if (includeHistory !== undefined && !['true', 'false'].includes(includeHistory)) {
    return res.status(400).json({
      error: true,
      message: 'IncludeHistory parameter must be "true" or "false"'
    });
  }

  // Sanitize query and store in req.query for consistency
  req.query.query = query.trim();

  next();
};

/**
 * Validate session ID parameter
 */
const validateSessionId = (req, res, next) => {
  const { sessionId } = req.params;

  if (!sessionId || !validateUUID(sessionId)) {
    return res.status(400).json({
      error: true,
      message: 'Valid sessionId (UUID) is required'
    });
  }

  next();
};

/**
 * Validate app ID parameter
 */
const validateAppId = (req, res, next) => {
  const { appId } = req.params;

  if (!appId || !validateUUID(appId)) {
    return res.status(400).json({
      error: true,
      message: 'Valid appId (UUID) is required'
    });
  }

  next();
};

/**
 * Validate embed request (for iframe URL generation)
 */
const validateEmbedRequest = (req, res, next) => {
  const { appId } = req.params;
  const { theme, width, height } = req.query;
  const authToken = req.headers.authorization;

  // Validate appId (should be UUID)
  if (!appId || !validateUUID(appId)) {
    return res.status(400).json({
      error: true,
      message: 'Valid appId (UUID) is required'
    });
  }

  // Authorization is optional for embed requests (widget doesn't require auth)

  // Validate theme if provided
  if (theme && !['light', 'dark'].includes(theme)) {
    return res.status(400).json({
      error: true,
      message: 'Theme must be either "light" or "dark"'
    });
  }

  // Validate dimensions if provided (basic validation)
  if (width && typeof width !== 'string') {
    return res.status(400).json({
      error: true,
      message: 'Width must be a string (e.g., "400px", "100%")'
    });
  }

  if (height && typeof height !== 'string') {
    return res.status(400).json({
      error: true,
      message: 'Height must be a string (e.g., "600px", "100vh")'
    });
  }

  next();
};

/**
 * General request validation middleware
 */
const validateRequest = (req, res, next) => {
  // Check content type for POST/PUT requests
  if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
    if (!req.is('application/json')) {
      return res.status(400).json({
        error: true,
        message: 'Content-Type must be application/json'
      });
    }
  }

  // Check for common security headers
  const userAgent = req.get('User-Agent');
  if (!userAgent) {
    return res.status(400).json({
      error: true,
      message: 'User-Agent header is required'
    });
  }

  next();
};

/**
 * Error handling middleware for validation errors
 */
const handleValidationError = (error, req, res, next) => {
  if (error.type === 'entity.parse.failed') {
    return res.status(400).json({
      error: true,
      message: 'Invalid JSON in request body'
    });
  }

  if (error.type === 'entity.too.large') {
    return res.status(413).json({
      error: true,
      message: 'Request body too large'
    });
  }

  next(error);
};

module.exports = {
  validateChatRequest,
  validateSessionId,
  validateAppId,
  validateEmbedRequest,
  validateRequest,
  handleValidationError
};
